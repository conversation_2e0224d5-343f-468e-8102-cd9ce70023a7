'use client';

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { TrendingUp, TrendingDown, Target, StopCircle } from 'lucide-react';
import { OrderExecutionType, OrderType, ChartOrderPlacement, BidAskPrice } from '@/types/trading';
import { ORDER_EXECUTION_TYPES, validateOrderPrice } from '@/utils/tradingCalculations';
import { formatPrice } from '@/utils/tradingCalculations';

interface ChartContextMenuProps {
  isVisible: boolean;
  position: { x: number; y: number };
  clickedPrice: number;
  currentBidAsk: BidAskPrice;
  precision: number;
  onPlaceOrder: (orderData: ChartOrderPlacement) => void;
  onClose: () => void;
}

export default function ChartContextMenu({
  isVisible,
  position,
  clickedPrice,
  currentBidAsk,
  precision,
  onPlaceOrder,
  onClose
}: ChartContextMenuProps) {
  const [selectedOrderType, setSelectedOrderType] = useState<OrderExecutionType>('market');
  const [orderDirection, setOrderDirection] = useState<OrderType>('buy');
  const [executionPrice, setExecutionPrice] = useState<string>('');
  const [size, setSize] = useState<number>(0.1);
  const [stopLoss, setStopLoss] = useState<string>('');
  const [takeProfit, setTakeProfit] = useState<string>('');

  const menuRef = useRef<HTMLDivElement>(null);

  // Initialize execution price when menu opens or order type changes
  useEffect(() => {
    if (isVisible) {
      if (selectedOrderType === 'market') {
        setExecutionPrice('');
      } else {
        setExecutionPrice(clickedPrice.toFixed(precision));
      }
    }
  }, [isVisible, selectedOrderType, clickedPrice, precision]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isVisible, onClose]);

  // Validate order when inputs change - using useMemo to prevent infinite loops
  const validationResult = useMemo(() => {
    if (selectedOrderType !== 'market' && executionPrice) {
      const price = parseFloat(executionPrice);
      if (!isNaN(price)) {
        return validateOrderPrice(selectedOrderType, orderDirection, price, currentBidAsk);
      }
    }
    return { isValid: true, error: '' };
  }, [selectedOrderType, orderDirection, executionPrice, currentBidAsk.bid, currentBidAsk.ask]);

  const validationError = validationResult.isValid ? '' : validationResult.error || '';

  const handleOrderTypeChange = (executionType: OrderExecutionType) => {
    setSelectedOrderType(executionType);
    
    // Auto-set order direction based on execution type
    if (executionType === 'buyLimit' || executionType === 'buyStop') {
      setOrderDirection('buy');
    } else if (executionType === 'sellLimit' || executionType === 'sellStop') {
      setOrderDirection('sell');
    }
  };

  const handleSubmit = () => {
    if (validationError) return;

    // For pending orders, determine order direction from execution type
    let finalOrderDirection = orderDirection;
    if (selectedOrderType === 'buyLimit' || selectedOrderType === 'buyStop') {
      finalOrderDirection = 'buy';
    } else if (selectedOrderType === 'sellLimit' || selectedOrderType === 'sellStop') {
      finalOrderDirection = 'sell';
    }

    const orderData: ChartOrderPlacement = {
      price: selectedOrderType === 'market' ? clickedPrice : parseFloat(executionPrice),
      time: Date.now(),
      orderExecutionType: selectedOrderType,
      orderType: finalOrderDirection,
      size,
      stopLoss: stopLoss ? parseFloat(stopLoss) : undefined,
      takeProfit: takeProfit ? parseFloat(takeProfit) : undefined
    };

    onPlaceOrder(orderData);
    onClose();
  };

  const getOrderTypeInfo = (executionType: OrderExecutionType) => {
    return ORDER_EXECUTION_TYPES.find(type => type.value === executionType);
  };

  const getCurrentPrice = () => {
    return orderDirection === 'buy' ? currentBidAsk.ask : currentBidAsk.bid;
  };

  if (!isVisible) return null;

  return (
    <div
      ref={menuRef}
      className="fixed z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-4 min-w-80"
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -10px)'
      }}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between border-b pb-2">
          <h3 className="font-semibold text-gray-800">Place Order</h3>
          <span className="text-sm text-gray-600">
            @ {formatPrice(clickedPrice, precision)}
          </span>
        </div>

        {/* Current Market Prices */}
        <div className="flex justify-between text-sm">
          <div className="text-green-600">
            Ask: {formatPrice(currentBidAsk.ask, precision)}
          </div>
          <div className="text-red-600">
            Bid: {formatPrice(currentBidAsk.bid, precision)}
          </div>
        </div>

        {/* Order Type Selection */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Order Type</label>
          <div className="grid grid-cols-2 gap-2">
            {ORDER_EXECUTION_TYPES.map((type) => (
              <button
                key={type.value}
                onClick={() => handleOrderTypeChange(type.value)}
                className={`p-2 text-sm rounded border transition-colors ${
                  selectedOrderType === type.value
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100'
                }`}
              >
                {type.label}
              </button>
            ))}
          </div>
          {selectedOrderType !== 'market' && (
            <p className="text-xs text-gray-600">
              {getOrderTypeInfo(selectedOrderType)?.description}
            </p>
          )}
        </div>

        {/* Order Direction (for market orders) */}
        {selectedOrderType === 'market' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Direction</label>
            <div className="flex space-x-2">
              <button
                onClick={() => setOrderDirection('buy')}
                className={`flex-1 flex items-center justify-center p-2 rounded transition-colors ${
                  orderDirection === 'buy'
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <TrendingUp className="h-4 w-4 mr-1" />
                Buy
              </button>
              <button
                onClick={() => setOrderDirection('sell')}
                className={`flex-1 flex items-center justify-center p-2 rounded transition-colors ${
                  orderDirection === 'sell'
                    ? 'bg-red-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <TrendingDown className="h-4 w-4 mr-1" />
                Sell
              </button>
            </div>
          </div>
        )}

        {/* Execution Price (for pending orders) */}
        {selectedOrderType !== 'market' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Execution Price</label>
            <input
              type="number"
              value={executionPrice}
              onChange={(e) => setExecutionPrice(e.target.value)}
              step={Math.pow(10, -precision)}
              className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter price..."
            />
            {validationError && (
              <p className="text-xs text-red-600">{validationError}</p>
            )}
          </div>
        )}

        {/* Position Size */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Size (Lots)</label>
          <input
            type="number"
            value={size}
            onChange={(e) => setSize(parseFloat(e.target.value) || 0)}
            step="0.01"
            min="0.01"
            className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Stop Loss and Take Profit */}
        <div className="grid grid-cols-2 gap-2">
          <div className="space-y-1">
            <label className="block text-xs font-medium text-gray-700">Stop Loss</label>
            <input
              type="number"
              value={stopLoss}
              onChange={(e) => setStopLoss(e.target.value)}
              step={Math.pow(10, -precision)}
              className="w-full p-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Optional"
            />
          </div>
          <div className="space-y-1">
            <label className="block text-xs font-medium text-gray-700">Take Profit</label>
            <input
              type="number"
              value={takeProfit}
              onChange={(e) => setTakeProfit(e.target.value)}
              step={Math.pow(10, -precision)}
              className="w-full p-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Optional"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2 pt-2">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={!!validationError || size <= 0}
            className={`flex-1 px-4 py-2 text-sm rounded transition-colors ${
              validationError || size <= 0
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            Place Order
          </button>
        </div>
      </div>
    </div>
  );
}
